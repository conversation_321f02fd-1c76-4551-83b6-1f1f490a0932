patchbuild build -p release -d \
  -j 20 \
  --clang \
  --include-commit \
  --binary-strip \
  --use-branch="comm2:origin/master" \
  --use-branch="oss2/mmpaylas:origin/mmpaylas_fPIC" \
  --use-branch="mm3rd2/pybind11:origin/pybind11-2.13.6" \
  --use-branch="mm3rd2/gperftools:origin/gperftools-2.0-no-tcmalloc" \
  --use-branch="mm3rd2/uuid:origin/master_so" \
  --use-branch="mm3rd2/bfd:origin/master_so" \
  --use-branch="mm3rd2/iconv:origin/master_so" \
  --use-branch="mm3rd2/python3:origin/python3-3.12.8_so" \
  --use-branch="mm3rd2/protobuf:origin/master_fPIC" \
  --use-branch="mm3rd2/iberty:origin/iberty-2.24-fPIC" \
  --use-branch="mm3rd2/boost-target:origin/master_fPIC" \
  --use-branch="mm3rd2/lz4:origin/lz4-1.8.2-fPIC" \
  --use-branch="mm3rd2/icu:origin/icu-4.8.1-fPIC" \
  --use-branch="mm3rd2/iconv:origin/iconv-1.16-fPIC" \
  --use-branch="platform:origin/feat_kvclient_release" \
  --use-branch="kvstore:origin/feat_kvclient_release" \
  :minirag.so
#   --personal-host \
#   :libdify.so