#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <error.h>
#include <string.h>
#include <sstream>

#include <vector>
#include <map>
#include <unordered_map>
#include <string>
#include <utility>
#include <pybind11/pybind11.h>
#include "oss/mmlas/mmlas_api.h"
#include "dify.hh"

using namespace dify;

int las_init_so(const std::string& so_path) {
  return mmlas::InitWithSo(so_path.c_str());
}

int las_init_client() { return mmlas::InitForClient(); }

std::string DeleteCronJob(std::string user_id, std::string room_id,
                          std::string wx_robot_id,
                          std::vector<std::string> task_ids) { // NOLINT
  std::stringstream ss;
  ss << "user_id" << user_id << "\n";
  ss << "room_id" << room_id << "\n";
  ss << "wx_robot_id" << wx_robot_id << "\n";
  ss << "task_ids:";
  for (const auto &idx : task_ids) {
    ss << idx << ",";
  }
  return ss.str();
}

PYBIND11_MODULE(libdify, m) {
  m.def("las_init_so", &las_init_so, "las init so");
  m.def("las_init_client", &las_init_client, "las init client");

  pybind11::class_<PyToolKv>(m, "PyToolKv")
      .def(pybind11::init())
      .def("get", &PyToolKv::get, "get key")
      .def("set", &PyToolKv::set, pybind11::arg("key"), pybind11::arg("val"),
           pybind11::arg("expire_timestamp") = 0, "set key-val");

  pybind11::class_<PyTableKv>(m, "PyTableKv")
      .def(pybind11::init())
      .def("trans", &PyTableKv::Transaction, pybind11::arg("use_tso"),
           pybind11::arg("auto_commit"), "begin transaction")
      .def("commit", &PyTableKv::Commit, pybind11::arg("committed") = true,
           "trans commit")
      .def("rollback", &PyTableKv::Rollback, "trans rollback")
      .def("query", &PyTableKv::Query, pybind11::arg("uin"),
           pybind11::arg("sql"), pybind11::arg("result"), pybind11::arg("opts"),
           "sql query");

   m.def("delete_cron_job", &DeleteCronJob, " 删除定时提醒");  // 绑定函数
}


