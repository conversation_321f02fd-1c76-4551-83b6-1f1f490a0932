#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <error.h>
#include <string.h>

#include <vector>
#include <map>
#include <unordered_map>
#include <string>
#include <utility>
#include <pybind11/pybind11.h>
#include "comm2/svrkit/skclosuretimeout.h"
#include "weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/mmfinderdrassistantsvr.pb.h"
#include "weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/tool_kv/tool_kv.h"
#include "weixin/mmfinderluban/comm/ttlkv_util/ttlkv_util.h"
#include "platform/kvsvr_base_pb/cli/string_cli/string_cli.h"
#include "dify.hh"

using namespace dify;

pybind11::dict PyToolKv::get(const std::string& key) {
  Comm::SKClosureTimeout timeout(600);

  int get_code = 0;
  mmfinderdrassistantsvr::KvItem kv_store_item =
      wxassistant::ToolKv(key).get_kv_item(get_code);

  pybind11::dict item;
  item["value"] = kv_store_item.value();
  item["create_at"] = kv_store_item.create_at();
  item["update_at"] = kv_store_item.update_at();

  pybind11::dict result;
  result["ret_code"] = get_code;
  result["kv_item"] = item;
  return result;
}

int PyToolKv::set(const std::string& key, const std::string& value,
                  uint64_t expire_timestamp) {
  Comm::SKClosureTimeout timeout(800);

  int set_ret =
      wxassistant::ToolKv(key).update_kv_item(value, expire_timestamp);
  return set_ret;
}

int PyToolKv::clear(const std::string& key) {
  String::StringClient client(::s1mple::Luban_Ttlkv_SH_Config, ::s1mple::Luban_Ttlkv_Prefix);
  int del_ret = client.Delete(key, nullptr);
  return del_ret;
}