#ifndef __DIFY_H__
#define __DIFY_H__

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <string>
#include "kvstore/kvsvr/cli/table5_cli/table5_cli.h"

namespace dify {

class PyToolKv {
 public:
  PyToolKv() = default;
  ~PyToolKv() = default;

  pybind11::dict get(const std::string& key);
  int set(const std::string& key, const std::string& val,
          uint64_t expire_timestamp = 0);
  int clear(const std::string& key);
};

class PyTableKv {
 public:
  PyTableKv();
  ~PyTableKv() = default;

  int Transaction(const bool use_tso, const bool auto_commit);
  pybind11::dict Commit(const bool committed = true);
  pybind11::dict Rollback();

  int Query(uint64_t uin, const std::string& sql, pybind11::list& result,
            const pybind11::dict& opts);

 private:
  Table5::Table5Client client_;
};

}  // namespace dify

#endif  // __DIFY_H__
