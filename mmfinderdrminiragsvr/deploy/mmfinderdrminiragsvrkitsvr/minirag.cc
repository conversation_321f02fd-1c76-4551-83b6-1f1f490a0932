#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <error.h>
#include <string.h>

#include <vector>
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <string>
#include <utility>
#include <pybind11/pybind11.h>
#include "comm2/svrkit/skclosuretimeout.h"
#include "dify.hh"
#include <chrono>
#include <string>
#include <functional>
#include <openssl/md5.h>
#include <iomanip>
#include "iLogInternal.h"
#include "prservice/mmspropvpsvr_gpu/mmspropvpsvrclient.h"
#include "mmfddatagateway/mmfddataubfsvr/mmfddataubfsvrreqhelper.h"
#include "mmfddatagateway/mmfddataubf/mmfddataubf_utils.h"
#include "mmfddataubf_def.h"
#include "mmfddataubfsvrclient.h"

pybind11::dict get_ubf(uint64_t uin) {
     pybind11::dict item;
     MMFDDataUBFSvrClient ubfClient;
     mmfddataubf::UBFRequest ubfReq;
     ubfReq.set_access_key("t26B6Xr4");
     ubfReq.set_uin(uin);
     mmfddataubf::UBFResponse ubfResp;
     int ubfRet = ubfClient.GetUserBaseFeature(uin, ubfReq, ubfResp);
     item["ubf_ret"] = ubfRet;
     item["ubf_resp"] = ubfResp.DebugString();
     return item;
}

PYBIND11_MODULE(minirag, m) {
  m.def("get_ubf", &get_ubf);
  pybind11::class_<dify::PyToolKv>(m, "PyToolKv")
      .def(pybind11::init())
      .def("get", &dify::PyToolKv::get, "get key")
      .def("set", &dify::PyToolKv::set, pybind11::arg("key"), pybind11::arg("val"),
           pybind11::arg("expire_timestamp") = 0, "set key-val")
      .def("clear", &dify::PyToolKv::clear, "clear key");

  pybind11::class_<dify::PyTableKv>(m, "PyTableKv")
      .def(pybind11::init())
      .def("trans", &dify::PyTableKv::Transaction, pybind11::arg("use_tso"),
           pybind11::arg("auto_commit"), "begin transaction")
      .def("commit", &dify::PyTableKv::Commit, pybind11::arg("committed") = true,
           "trans commit")
      .def("rollback", &dify::PyTableKv::Rollback, "trans rollback")
      .def("query", &dify::PyTableKv::Query, pybind11::arg("uin"), pybind11::arg("sql"),
           pybind11::arg("result"), pybind11::arg("opts"), "sql query");
}

