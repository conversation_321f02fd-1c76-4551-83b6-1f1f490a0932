#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <error.h>
#include <string.h>

#include <vector>
#include <map>
#include <unordered_map>
#include <memory>
#include <string>
#include <utility>
#include <pybind11/pybind11.h>
#include "comm2/svrkit/skclosuretimeout.h"
#include "weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/mmfinderdrassistantsvr.pb.h"
#include "weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/tool_kv/tool_kv.h"
#include "platform/kv6/newkv6svrclient.h"
#include "kvstore/kvsvr/cli/transaction/transaction_cli.h"
#include "weixin/mmidc/api/info/mmidcbaseinfo.h"
#include "dify.hh"

using namespace dify;

const char* cfg = "/home/<USER>/etc/client/mmkvcfgsvr_byset_cli.conf";
const char* prefix = "mmfinderdrassistantnewtablefivekv";
const char* tabdef =
    "/home/<USER>/etc/client/global/"
    "mmfinderdrassistantnewtablefivekv_tabledef.ini";
const char* test_tabdef =
    "/home/<USER>/etc/client/global/mmtestenvtablefivekv_tabledef.ini";

PyTableKv::PyTableKv() : client_(cfg, prefix, MMIDCBaseInfo::GetLocalIDC()) {}

int PyTableKv::Transaction(const bool use_tso, const bool auto_commit) {
  return client_.BeginTransaction(use_tso, auto_commit);
}

pybind11::dict to_dict(const KvsvrStore::Transaction::TransResult& result) {
  pybind11::dict ret;
  ret["trxid"] = result.trxid;
  ret["coordinator"] = result.coordinator;
  ret["ret"] = result.ret;
  if (result.IsPrepare()) {
    ret["state"] = "prepare";
  } else if (result.IsCommit()) {
    ret["state"] = "commit";
  } else if (result.IsRollback()) {
    ret["state"] = "rollback";
  } else {
    ret["state"] = "unknown";
  }
  return ret;
}

pybind11::dict PyTableKv::Commit(const bool committed) {
  return to_dict(client_.CommitTransaction(committed));
}

pybind11::dict PyTableKv::Rollback() {
  return to_dict(client_.RollbackTransaction());
}

int PyTableKv::Query(uint64_t uin, const std::string& sql,
                     pybind11::list& result, const pybind11::dict& opts) {
  fivekv::sql::QueryOptions queryOpts;
  fivekv::sql::QueryOptions* optPtr = nullptr;
  if (opts.contains("table_id")) {
    queryOpts.table_id = opts["table_id"].cast<uint16_t>();
    optPtr = &queryOpts;
  }

  std::unique_ptr<Table5::SqlQueryResult> ptr(new Table5::SqlQueryResult());
  int ret = client_.SqlQuery(uin, sql, ptr, optPtr);
  if (ret != 0) {
    return ret;
  }

  size_t rowSize = ptr->GetRowsCount();
  size_t colSize = ptr->GetColumnsCount();
  for (size_t idx = 0; idx < rowSize; ++idx) {
    pybind11::dict item;
    for (size_t colIdx = 0; colIdx < colSize; ++colIdx) {
      const std::string& colName = ptr->GetColumnName(colIdx);
      fivekv::sql::AnyValue value = ptr->GetValue(idx, colIdx);

      tabledef::Table::Field::Type colType = ptr->GetColumnType(colIdx);
      switch (colType) {
        case tabledef::Table::Field::kString:
          item[colName.c_str()] = value.AsString();
          break;
        case tabledef::Table::Field::kBytes:
          item[colName.c_str()] = pybind11::bytes(value.AsBytes());
          break;
        case tabledef::Table::Field::kBool:
          item[colName.c_str()] = value.AsBool();
          break;
        case tabledef::Table::Field::kInt8:
          item[colName.c_str()] = static_cast<int>(value.AsInt8());
          break;
        case tabledef::Table::Field::kInt16:
          item[colName.c_str()] = static_cast<int>(value.AsInt16());
          break;
        case tabledef::Table::Field::kInt32:
          item[colName.c_str()] = value.AsInt32();
          break;
        case tabledef::Table::Field::kUint8:
          item[colName.c_str()] = static_cast<uint32_t>(value.AsUint8());
          break;
        case tabledef::Table::Field::kUint16:
          item[colName.c_str()] = static_cast<uint32_t>(value.AsUint16());
          break;
        case tabledef::Table::Field::kFlag32:
          item[colName.c_str()] = value.AsFlag32();
          break;
        case tabledef::Table::Field::kUint32:
          item[colName.c_str()] = value.AsUint32();
          break;
        case tabledef::Table::Field::kFloat:
          item[colName.c_str()] = value.AsFloat();
          break;
        case tabledef::Table::Field::kDouble:
          item[colName.c_str()] = value.AsDouble();
          break;
        case tabledef::Table::Field::kInt64:
          item[colName.c_str()] = value.AsInt64();
          break;
        case tabledef::Table::Field::kUint64:
          item[colName.c_str()] = value.AsUint64();
          break;
        default:
          break;
      }
    }
    result.append(item);
  }

  return ret;
}

