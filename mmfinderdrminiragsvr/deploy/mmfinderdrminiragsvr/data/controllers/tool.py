from fastapi import APIRouter
from models.response import StandardResponse
from models.tool import batchGetRequest, batchGetResponse, batchSetRequest
from utils.kv import TtlKv

router = APIRouter()


@router.post("/batch_get", response_model=StandardResponse[batchGetResponse])
async def batch_get(request: batchGetRequest):
    ttlkv = TtlKv()
    values = await ttlkv.async_batch_get(request.keys)
    response = batchGetResponse(values=values)
    return StandardResponse(data=response)


@router.post("/batch_set", response_model=StandardResponse[bool])
async def batch_set(request: batchSetRequest):
    ttlkv = TtlKv()
    ret = await ttlkv.async_batch_set(
        request.keys, request.values, request.expire_timestamp
    )
    return StandardResponse(data=ret)
