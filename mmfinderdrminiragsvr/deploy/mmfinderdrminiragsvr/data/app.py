from contextlib import asynccontextmanager

from controllers import tool
from fastapi import FastAP<PERSON>


@asynccontextmanager
async def lifespan(app: FastAPI):
    yield


app = FastAPI(lifespan=lifespan)


@app.get("/health")
async def health():
    return {"status": "OK"}


app.include_router(tool.router, prefix="/tool")

if __name__ == "__main__":
    import sys

    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=int(sys.argv[1]))
