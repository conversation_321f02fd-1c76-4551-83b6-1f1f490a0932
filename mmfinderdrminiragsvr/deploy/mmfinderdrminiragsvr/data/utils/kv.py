# -*- coding: utf-8 -*-
import asyncio
import sys

sys.path.append("/home/<USER>/mmfinderdrminiragsvr/so")
import minirag


class TtlKv:
    def __init__(self):
        self.kv_cli = minirag.PyToolKv()

    def set(self, key, value, expire_timestamp=0):
        ret = self.kv_cli.set(key, value, expire_timestamp)
        if ret != 0:
            raise Exception(f"set key {key} failed, ret: {ret}")
        return ret

    def get(self, key):
        ret = self.kv_cli.get(key)
        if ret.get("ret_code") != 0:
            raise Exception(f"get key {key} failed, ret: {ret.get('ret_code')}")
        return ret.get("kv_item").get("value")

    async def async_get(self, key):
        ret = await asyncio.to_thread(self.get, key)
        return ret

    async def async_set(self, key, value, expire_timestamp=0):
        ret = await asyncio.to_thread(self.set, key, value, expire_timestamp)
        return ret

    async def async_batch_get(self, keys: list[str]) -> dict[str, str]:
        values = {}

        async def fetch_single_value(key):
            try:
                value = await self.async_get(key)
                return key, value
            except Exception:
                return key, ""

        results = await asyncio.gather(*[fetch_single_value(key) for key in keys])
        for key, value in results:
            values[key] = value
        return values

    async def async_batch_set(
        self, keys: list[str], values: list[str], expire_timestamp=0
    ) -> bool:
        async def set_single_value(key, value):
            ret = await self.async_set(key, value, expire_timestamp)
            return ret

        rets = await asyncio.gather(
            *[set_single_value(key, value) for key, value in zip(keys, values)]
        )
        if any(ret != 0 for ret in rets):
            return False
        return True


if __name__ == "__main__":
    try:
        # 创建 PyToolKv 实例
        tool_kv = minirag.PyToolKv()

        # 1. 使用 set() 设置键值对
        key = "lhd_test_key"
        value = "hello_from_cpp"
        print(f"设置键值: key='{key}', value='{value}'")
        tool_kv.set(key, value)  # 第三个参数 expire_timestamp 使用默认值 0

        # 2. 使用 get() 获取值
        retrieved_value = tool_kv.get(key)
        print(f"获取键 '{key}' 的值: '{retrieved_value.get('kv_item').get('value')}'")
        # assert retrieved_value.get('kv_item').get('value') == value

        retrieved_value_after_clear = tool_kv.get(key)
        print(f"清除后再次获取键 '{key}' 的值: '{retrieved_value_after_clear}'")
        print("PyToolKv 演示完毕。")

    except Exception as e:
        print(f"操作 PyToolKv 时发生错误: {e}")
