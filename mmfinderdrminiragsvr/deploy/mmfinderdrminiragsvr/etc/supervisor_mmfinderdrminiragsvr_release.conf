[unix_http_server]
file=/tmp/mmfinderdrminiragsvr_mmfinderdrminiragsvr.supervior.sock
chmod=0760
chown=qspace:users

[supervisord]
nodaemon=true
logfile=/tmp/mmfinderdrminiragsvr_mmfinderdrminiragsvr_supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

[supervisorctl]
serverurl = unix:///tmp/mmfinderdrminiragsvr_mmfinderdrminiragsvr.supervior.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

; use nodaemon to start proc
[program:api]
command=sh -c "killall -9 api;/home/<USER>/mmfinderdrminiragsvr/bin/api 8080"
autostart=true
autorestart=true
stopsignal=KILL
stopasgroup=true
killasgroup=true