#!/bin/sh
if [ $# -eq 0 ]; then
    echo "No listen port provided. Exiting."
    exit 1
fi
source /home/<USER>/conda/etc/profile.d/conda.sh
conda activate py12
cd /home/<USER>/mmfinderdrminiragsvr/data/
ln -sf /home/<USER>/conda/envs/py12/lib/libpython3.12.so.1.0 /home/<USER>/mmfinderdrminiragsvr/so/
echo 'export LD_LIBRARY_PATH=/home/<USER>/conda/envs/py12/lib:$LD_LIBRARY_PATH' >> ~/.bashrc
export LD_LIBRARY_PATH=/home/<USER>/conda/envs/py12/lib:$LD_LIBRARY_PATH
gunicorn -w 24 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$@ --timeout 600 --max-requests 1000 --max-requests-jitter 100 app:app
exit $?