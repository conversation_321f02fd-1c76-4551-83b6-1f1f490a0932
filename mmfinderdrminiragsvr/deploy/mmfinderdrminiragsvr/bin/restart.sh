
#!/usr/bin/sh

# killall

supervisorTestPath="/home/<USER>/mmfinderdrminiragsvr/etc/supervisor_test.conf"

ps aux | grep "/usr/bin/supervisord" | grep -v grep

# use supervisord
if [ $? -eq 0 ]
then
    supervisorctl -c ${supervisorTestPath} restart all

elif [ -f ${supervisorTestPath} ]
then
    nohup /usr/bin/supervisord -c ${supervisorTestPath} >/home/<USER>/data/mmfinderdrminiragsvr/supervisor_test.log 2>&1 &

# not use supervisord
else
    killall -9 mmfinderdrminiragsvr

    # command must be runned as daemon
    /home/<USER>/mmfinderdrminiragsvr/sbin/mmfinderdrminiragsvr -i /home/<USER>/mmfinderdrminiragsvr/etc/mmfinderdrminiragsvr.conf -d
fi