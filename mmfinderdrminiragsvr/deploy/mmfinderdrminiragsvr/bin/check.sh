#!/bin/sh

# 执行脚本 /var/p6n/las.sh
/var/p6n/las.sh
if [ $? -ne 0 ]; then
    exit -1;
fi

# 检查 8080 端口
CHECK=$(netstat -ant | grep LISTEN | grep "0.0.0.0:8080" | wc -l)
if [ $CHECK -eq 0 ]; then
    echo "check fail";
    exit -1;
fi;

# 调用一个test api，确保是一定能调通的
# 定义请求参数
API_URL="http://0.0.0.0:8080/toolapi/test"
TIMEOUT=10  # 超时时间（秒）
# 发送 GET 请求（带超时和错误处理, 3次重试）
MAX_RETRY=3
RETRY_DELAY=1
for i in $(seq 1 $MAX_RETRY); do
    response=$(curl -sS -w "\nHTTP_CODE:%{http_code}" \
    --max-time "$TIMEOUT" \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H "Accept: application/json" \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36' \
    -H 'accept: application/json' \
    -H 'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"' \
    -H 'sec-ch-ua-mobile: ?0' \
    "$API_URL" 2>&1)
    if [ $? -eq 0 ]; then
        break
    fi
    sleep $RETRY_DELAY
done

# 分离 HTTP 状态码和内容
http_code=$(echo "$response" | awk -F 'HTTP_CODE:' '{print $2}')
response_content=$(echo "$response" | sed 's/HTTP_CODE:.*//')
echo -e "$http_code"
# 验证 HTTP 状态码
if [ "$http_code" -ne 200 ]; then
    echo -e "\033[33m[WARN] 异常状态码: HTTP $http_code\033[0m"
    echo "响应内容: $response_content"
    exit -1
fi

# 字符串匹配验证
EXPECTED_STRING="test"  # 预期匹配的字符串
if [[ "$response_content" != *"$EXPECTED_STRING"* ]]; then
    echo -e "\033[31m[FAIL] 未找到预期字符串 '$EXPECTED_STRING'\033[0m"
    echo "实际响应内容:"
    echo "$response_content"
    exit -1
fi
# 结束
echo -e "\033[32m[SUCCESS] 响应包含预期字符串\033[0m"
echo "check success";
exit 0;


