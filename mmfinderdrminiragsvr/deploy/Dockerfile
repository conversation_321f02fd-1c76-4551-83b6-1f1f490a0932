FROM weregistry.woa.com/kristendi/difypy12base:latest
USER root
RUN yum --enablerepo=TencentOS-testing install -y ffmpeg ffmpeg-devel
SHELL [ "/bin/bash", "--login", "-c" ]
ARG MODULENAME=mmfinderdrminiragsvr
ARG pyenv=py12

USER qspace:users
COPY --chown=qspace:users ./${MODULENAME} /home/<USER>/${MODULENAME}
RUN chmod -R 755 /home/<USER>/${MODULENAME}

RUN mkdir -p /home/<USER>/log/mmfinderdrminiragsvr/
RUN conda activate ${pyenv} && pip install -r /home/<USER>/${MODULENAME}/data/requirements.txt
WORKDIR /home/<USER>/${MODULENAME}/data/
CMD bash
