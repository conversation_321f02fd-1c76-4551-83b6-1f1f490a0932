load("/code_generator/docker", "docker_img")
filegroup(
    name = "mmfinderdrminiragsvr_conf",
    srcs = glob(["deploy/mmfinderdrminiragsvr/etc/*"])
)

filegroup(
    name = "mmfinderdrminiragsvr_bin",
    srcs = glob(["deploy/mmfinderdrminiragsvr/bin/*"])
)

filegroup(
    name = "mmfinderdrminiragsvr_data",
    srcs = glob(["deploy/mmfinderdrminiragsvr/data/*"])
)

filegroup(
    name = "mmfinderdrminiragsvr_sbin",
    srcs = glob(["deploy/mmfinderdrminiragsvr/sbin/*"])
)

docker_img(
    name = "mmfinderdrminiragsvr_docker",
    image_name = "mmfinderdrminiragsvr",
    module_name = "mmfinderdrminiragsvr",
    srcs = [
        "deploy/Dockerfile",
    ],
    etc = [":mmfinderdrminiragsvr_conf"],
    bin = [":mmfinderdrminiragsvr_bin"],
    data= [":mmfinderdrminiragsvr_data"],
    sbin = [":mmfinderdrminiragsvr_sbin"],
    extra_args = '--network=host',
)